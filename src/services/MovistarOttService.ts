import type { IMovistarOttService } from "./impl/IMovistarOttService";

const BASE_URL = import.meta.env.VITE_MOVISTAR_API_URL;
const BEARER_TOKEN = import.meta.env.VITE_MOVISTAR_API_KEY;

// Cliente MCP para React
class MCPClient {
  private sessionId: string | null = null;

  constructor() { }

  async request(method: string, params: any = {}): Promise<any> {
    const requestBody: any = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: method,
      params: params
    };

    // El sessionId se enviará como header, no en el body

    console.log(`🚀 Enviando request MCP:`, {
      method,
      params,
      body: requestBody,
      sessionId: this.sessionId
    });

    const headers: any = {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/event-stream',
      'Authorization': `Bearer ${BEARER_TOKEN}`
    };

    // Añadir sessionId como header si está disponible (excepto para initialize)
    if (this.sessionId && method !== 'initialize') {
      headers['X-Session-ID'] = this.sessionId;
    }

    console.log(`📤 Headers enviados:`, headers);

    const response = await fetch(`${BASE_URL}/mcp`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ HTTP Error ${response.status}:`, {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    const contentType = response.headers.get('content-type');
    console.log('Content-Type:', contentType);

    // Handle Server-Sent Events (SSE) response
    if (contentType?.includes('text/event-stream')) {
      const text = await response.text();
      console.log('SSE Response:', text);

      // Parse SSE format to extract JSON data and session ID
      const lines = text.split('\n');
      let jsonData = '';
      let extractedSessionId = '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          jsonData = line.substring(6); // Remove 'data: ' prefix
        } else if (line.startsWith('id: ')) {
          extractedSessionId = line.substring(4); // Remove 'id: ' prefix
        }
      }

      // Store session ID for future requests
      if (extractedSessionId && method === 'initialize') {
        this.sessionId = extractedSessionId;
        console.log('🔑 Session ID extraído:', this.sessionId);
      }

      if (jsonData) {
        try {
          const data = JSON.parse(jsonData);
          if (data.error) {
            throw new Error(`MCP Error ${data.error.code}: ${data.error.message}`);
          }
          return data.result;
        } catch (parseError) {
          console.error('Error parsing SSE JSON:', parseError);
          throw new Error(`Error parsing SSE response: ${parseError}`);
        }
      } else {
        throw new Error('No data found in SSE response');
      }
    } else {
      // Handle regular JSON response
      const data = await response.json();

      if (data.error) {
        throw new Error(`MCP Error ${data.error.code}: ${data.error.message}`);
      }

      return data.result;
    }
  }

  // Inicializar conexión MCP
  async initialize(): Promise<any> {
    try {
      const result = await this.request('initialize', {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'React MCP Client',
          version: '1.0.0'
        }
      });

      console.log('MCP inicializado:', result);
      return result;
    } catch (error) {
      console.error('Error inicializando MCP:', error);
      throw error;
    }
  }

  // Listar herramientas disponibles
  async listTools(): Promise<any[]> {
    try {
      const result = await this.request('tools/list', {});
      console.log('Herramientas disponibles:', result);
      return result.tools || result;
    } catch (error) {
      console.error('Error listando herramientas:', error);
      throw error;
    }
  }

  // Ejecutar herramienta
  async callTool(toolName: string, args: any = {}): Promise<any> {
    try {
      const result = await this.request('tools/call', {
        name: toolName,
        arguments: args
      });

      console.log(`Resultado de ${toolName}:`, result);
      return result;
    } catch (error) {
      console.error(`Error ejecutando ${toolName}:`, error);
      throw error;
    }
  }
}

export class MovistarOttService implements IMovistarOttService {
  private static instance: MovistarOttService;
  private client: MCPClient | null = null;
  private tools: any[] = [];
  private initialized: boolean = false;
  private loading: boolean = false;
  private error: string | null = null;

  private constructor() {}

  static getInstance(): MovistarOttService {
    if (!MovistarOttService.instance) {
      MovistarOttService.instance = new MovistarOttService();
      MovistarOttService.instance.init();
    }
    return MovistarOttService.instance;
  }

  private async init(): Promise<void> {
    if (!BASE_URL || !BEARER_TOKEN) {
      this.error = 'BASE_URL y BEARER_TOKEN son requeridos';
      return;
    }

    try {
      this.loading = true;
      this.error = null;

      this.client = new MCPClient();
      await this.client.initialize();

      this.tools = await this.client.listTools();
      this.initialized = true;

      console.log('✅ Cliente MCP inicializado correctamente');
    } catch (err: any) {
      console.error('❌ Error inicializando cliente MCP:', err);
      this.error = err.message;
    } finally {
      this.loading = false;
    }
  }

  async initialize(): Promise<any> {
    if (!this.client) {
      await this.init();
    }
    return this.client?.initialize();
  }

  async listTools(): Promise<any[]> {
    if (!this.client || !this.initialized) {
      throw new Error('Cliente MCP no inicializado');
    }
    return this.tools;
  }

  async callTool(toolName: string, args: any = {}): Promise<any> {
    if (!this.client || !this.initialized) {
      throw new Error('Cliente MCP no inicializado');
    }

    this.loading = true;
    this.error = null;

    try {
      const result = await this.client.callTool(toolName, args);
      return result;
    } catch (err: any) {
      this.error = err.message;
      throw err;
    } finally {
      this.loading = false;
    }
  }

  async searchMovies(title: string): Promise<any> {
    return this.callTool('getFilmsByTitleOTT', { title });
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  isLoading(): boolean {
    return this.loading;
  }

  getError(): string | null {
    return this.error;
  }
}
