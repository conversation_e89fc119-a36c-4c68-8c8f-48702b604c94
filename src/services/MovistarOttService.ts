import type { IMovistarOttService } from "./impl/IMovistarOttService";

const BASE_URL = import.meta.env.VITE_MOVISTAR_API_URL;
const API_KEY = import.meta.env.VITE_MOVISTAR_API_KEY;

export class MovistarOttService implements IMovistarOttService {
  private static instance: MovistarOttService;

  private constructor() {}

  static getInstance(): MovistarOttService {
    if (!MovistarOttService.instance) {
      MovistarOttService.instance = new MovistarOttService();
      MovistarOttService.instance.init();
    }
    return MovistarOttService.instance;
  }

  private async init(): Promise<void> {}

}
