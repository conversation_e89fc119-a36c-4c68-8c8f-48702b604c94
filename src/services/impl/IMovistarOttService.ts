export interface IMovistarOttService {
  initialize(): Promise<any>;
  listTools(): Promise<any[]>;
  callTool(toolName: string, args?: any): Promise<any>;
  searchMovies(title: string): Promise<any>;
  searchByActor(actor: string): Promise<any>;
  searchByTitle(title: string): Promise<any>;
  searchByGenre(genre: string): Promise<any>;
  getRecommendations(): Promise<any>;
  close(): Promise<void>;
  getAvailableTools(): any[];
  isInitialized(): boolean;
  isLoading(): boolean;
  getError(): string | null;
}
