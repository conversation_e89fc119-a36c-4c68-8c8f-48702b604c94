import axios from "axios";
import type { IAppService } from "./impl/IAppService";
import { VoicesService } from "./VoicesService";
import type { GenerateInput } from "../types/GenerateInput";
import type { GenerateResponse } from "../types/GenerateResponse";
import type { SelectedPresetResponse } from "../types/SelectedPresetResponse";
import { playAudioWithFallback } from "../utils/audioUtils";
import { ConversationStorage } from "./ConversationStorage";
import {
  extractApiResponseText,
  processResponseText,
  createApiHeaders,
  handleApiRequest
} from "../utils/apiUtils";

// --- CONFIGURACIÓN ---
// Carga de variables de entorno para la configuración del servicio.
// Esto permite cambiar la configuración sin modificar el código.
const BASE_URL = import.meta.env.VITE_LLM_API_URL;
const API_KEY = import.meta.env.VITE_LLM_API_KEY;
const PRESET_GENCHARBOT = import.meta.env.VITE_LLM_PRESETID_GENCHARBOT;
const PRESET_IA_VS_PLAYER = import.meta.env.VITE_LLM_PRESETID_IA_VS_PLAYER;

/**
 * AppService es la clase principal que gestiona la lógica de negocio y la comunicación
 * con la API del modelo de lenguaje.
 *
 * Utiliza el patrón Singleton para asegurar que solo exista una única instancia de este servicio
 * en toda la aplicación, manteniendo un estado consistente (como el ID de sesión).
 */
export class AppService implements IAppService {
  private static instance: AppService;
  private sesid = "";
  private selectedPreset = PRESET_GENCHARBOT;
  private voicesService: VoicesService;
  private audioGeneratedCallback?: (audioUrl: string) => void;
  private audioFinishedCallback?: () => void;
  private audioStartedCallback?: () => void;
  private conversationStorage = ConversationStorage.getInstance();

  private constructor() {
    this.voicesService = VoicesService.getInstance();
  }

  static getInstance(): AppService {
    if (!AppService.instance) {
      AppService.instance = new AppService();
      AppService.instance.init();
    }
    return AppService.instance;
  }

  private async init(): Promise<void> {
    const existingSessId = localStorage.getItem("sessid");

    if (existingSessId) {
      this.sesid = existingSessId;
    } else {
      try {
        const response = await this.selectPreset();
        this.sesid = response.sesid;
        localStorage.setItem("sessid", this.sesid);
      } catch (error) {
        console.error("Error al inicializar el servicio:", error);
      }
    }

    // Configurar la voz una sola vez al inicializar
    try {
      console.log("🔊 Configurando servicio de voces al inicializar...");
      const voiceConfigured = await this.voicesService.configVoice();
      if (voiceConfigured) {
        console.log("✅ Servicio de voces configurado correctamente");
      } else {
        console.warn("⚠️ No se pudo configurar el servicio de voces");
      }
    } catch (error) {
      console.error("❌ Error configurando servicio de voces:", error);
    }
  }

  /**
   * Establece manualmente el ID de sesión.
   * @param id El nuevo ID de sesión.
   */
  setSesid(id: string): void {
    this.sesid = id;
  }

  /**
   * Establece el preset a utilizar
   * @param preset "gencharbot" | "ia_vs_player"
   * @returns void
   */
  async setPreset(preset: "gencharbot" | "ia_vs_player"): Promise<void> {
    switch (preset) {
      case "gencharbot":
        this.selectedPreset = PRESET_GENCHARBOT;
        break;
      case "ia_vs_player":
        this.selectedPreset = PRESET_IA_VS_PLAYER;
        break;
      default:
        console.warn(`Preset '${preset}' no reconocido.`);
        return;
    }

    return this.updatePreset();
  }

  /**
   * Llama a la API para actualizar el preset en el servidor y obtener un nuevo `sesid`
   * asociado a esa configuración.
   */
  private async updatePreset(): Promise<void> {
    try {
      const response = await this.selectPreset();
      this.sesid = response.sesid;
    } catch (error) {
      console.error("Error al actualizar el preset:", error);
    }
  }

  /**
   * Get HTTP headers for API requests
   * @returns Headers object with authentication and session info
   */
  private getHeaders(): Record<string, string> {
    return createApiHeaders(API_KEY || "", this.sesid);
  }

  /**
   * Generic handler for all API requests
   * Centralizes logging and error management
   * @param request - The axios request promise
   * @returns Response data
   */
  private async handleRequest<T>(request: Promise<any>): Promise<T> {
    return handleApiRequest<T>(request, import.meta.env.MODE === "development");
  }

  /**
   * Envía un texto (query) a la API para obtener una respuesta generada por el LLM.
   * @param text El texto o pregunta del usuario.
   * @param id Un ID opcional para la consulta.
   * @returns Una promesa con la respuesta de la API.
   */
  async generate(text: string, id?: string): Promise<GenerateResponse> {
    const data: GenerateInput = {
      id: { ses: this.sesid },
      preset: this.selectedPreset,
      query: text,
      query_args: {},
    };

    if (id) {
      data.query_args!.query_id = id;
    }

    return this.handleRequest<GenerateResponse>(
      axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
    );
  }

  /**
   * Llama al endpoint de la API para seleccionar un preset y obtener un `sesid`.
   * @returns Una promesa con la respuesta, que incluye el `sesid`.
   */
  async selectPreset(): Promise<SelectedPresetResponse> {
    return this.handleRequest<SelectedPresetResponse>(
      axios.get(`${BASE_URL}preset/${this.selectedPreset}`, {
        headers: this.getHeaders(),
      })
    );
  }

  // Método para configurar el callback de audio
  setAudioCallback(callback: (audioUrl: string) => void): void {
    this.audioGeneratedCallback = callback;
  }

  setAudioStartedCallback(callback: () => void): void {
    this.audioStartedCallback = callback;
  }

  // Método para configurar el callback de audio finalizado
  setAudioFinishedCallback(callback: () => void): void {
    this.audioFinishedCallback = callback;
  }

  // Método para obtener el callback de audio finalizado
  getAudioFinishedCallback(): (() => void) | undefined {
    return this.audioFinishedCallback;
  }

  // Método privado para generar audio automáticamente
  private async generateAudioForResponse(responseText: string): Promise<void> {
    try {
      // Verificar que la voz esté configurada
      if (!this.voicesService.isVoiceConfigured()) {
        console.warn("⚠️ Servicio de voces no configurado, no se puede generar audio");
        if (this.audioFinishedCallback) {
          setTimeout(() => {
            this.audioFinishedCallback!();
          }, 1000);
        }
        return;
      }

      // console.log("🔊 Generando audio para:", responseText);
      const audioBlob = await this.voicesService.getAudio(responseText);
      const audioUrl = URL.createObjectURL(audioBlob);

      // ✅ CORREGIDO: Usar la función importada directamente
      if (this.audioGeneratedCallback) {
        this.audioGeneratedCallback(audioUrl);

        // ✅ CORREGIDO: Llamar directamente sin require
        setTimeout(() => {
          playAudioWithFallback(
            audioUrl,
            this.audioFinishedCallback, // onEnded
            this.audioStartedCallback, // onStarted
            (error: string) => {
              // onError
              console.error("❌ Error reproduciendo audio:", error);
              // Ejecutar callback de finalización en caso de error
              if (this.audioFinishedCallback) {
                setTimeout(() => {
                  this.audioFinishedCallback!();
                }, 1000);
              }
            }
          );
        }, 100);
      }

      // console.log("✅ Audio generado exitosamente");
    } catch (error) {
      console.error("❌ Error generando audio:", error);
      // ✅ MEJORADO: Siempre ejecutar callback de finalización
      if (this.audioFinishedCallback) {
        setTimeout(() => {
          this.audioFinishedCallback!();
        }, 2000);
      }
    }
  }

  /**
   *
   * @param response
   * @returns
   */
  private async processIaVsPlayerResponse(
    response: GenerateResponse
  ): Promise<GenerateResponse> {
    try {
      console.log("🔍 Respuesta completa de la API:", {
        output: response.output,
        response: response.response,
        input: response.input,
      });

      // Extract response text using utility function
      const responseText = extractApiResponseText(response);

      // Always generate audio for any text response
      if (responseText && typeof responseText === "string") {
        // Process response text (handles both JSON and plain text)
        const { audioText, displayText } = processResponseText(responseText);

        // Save game progress if response contains JSON with game data
        try {
          const parsed = JSON.parse(responseText.trim().startsWith("{") ? responseText : "{}");
          if (parsed.respuesta || parsed.pista || parsed.cuenta_regresiva !== undefined) {
            this.updateGameProgress(parsed);
          }
        } catch (error) {
          // Not JSON or invalid JSON, continue with text processing
        }

        // Always generate audio if there's text
        if (audioText) {
          await this.generateAudioForResponse(audioText);
        }

        // Return response with formatted text
        return {
          ...response,
          response: displayText || responseText,
          output: displayText || responseText,
        };
      }

      // If no text found, return original response with debug message
      console.warn("⚠️ No se encontró texto en la respuesta:", response);
      return {
        ...response,
        response: extractApiResponseText(response, "No se pudo procesar la respuesta"),
      };
    } catch (error) {
      console.error("❌ Error procesando respuesta IA vs Player:", error);
      return response; // Return original response on error
    }
  }

  /**
   * Specific methods for each preset with different behaviors
   */
  async generateWithGenCharBot(
    text: string,
    id?: string
  ): Promise<GenerateResponse> {
    await this.setPreset("gencharbot");
    return this.generate(text, id);
  }

  /**
   * Genera una respuesta utilizando el preset IA vs Player
   * @param text
   * @param id
   * @param personaje
   * @returns
   */
  async generateWithIaVsPlayerSimple(
    text: string,
    id?: string,
    personaje?: string
  ): Promise<GenerateResponse> {
    await this.setPreset("ia_vs_player");

    // Construir el query incluyendo el personaje directamente en el texto
    let finalQuery = text;

    if (personaje) {
      if (text.includes("¡Hola! Estoy listo para adivinar")) {
        // Para el mensaje inicial, incluir el personaje claramente
        finalQuery = `El personaje que debes adivinar es: ${personaje}. ${text}`;
      }
      // Para otras preguntas, el contexto del personaje ya está en la conversación
    }

    const data: GenerateInput = {
      id: { ses: this.sesid },
      preset: this.selectedPreset,
      query: finalQuery,
      query_args: {},
    };

    if (id) {
      data.query_args!.query_id = id;
    }

    console.log(
      "📤 Datos enviados (método simple):",
      JSON.stringify(data, null, 2)
    );

    const response = await this.handleRequest<GenerateResponse>(
      axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
    );

    console.log("📥 Respuesta recibida:", JSON.stringify(response, null, 2));

    return this.processIaVsPlayerResponse(response);
  }

  /**
   * Genera una respuesta utilizando el preset IA vs Player
   * @param text
   * @param id
   * @param personaje
   * @returns
   */
  async generateWithIaVsPlayer(
    text: string,
    personaje?: string
  ): Promise<GenerateResponse> {
    await this.setPreset("ia_vs_player");

    // Construir el query incluyendo el personaje directamente en el texto
    let finalQuery = text;

    if (personaje) {
      if (text.includes("¡Hola! Estoy listo para adivinar")) {
        // Para el mensaje inicial, incluir el personaje claramente
        finalQuery = `El personaje que debes adivinar es: ${personaje}. ${text}`;
      }
      // Para otras preguntas, el contexto del personaje ya está en la conversación
    }

    const data: GenerateInput = {
      id: { ses: this.sesid },
      preset: this.selectedPreset,
      query: finalQuery,
      query_args: {},
    };

    console.log("📤 Datos enviados a la API:", JSON.stringify(data, null, 2));

    const response = await this.handleRequest<GenerateResponse>(
      axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
    );

    // console.log("📥 Respuesta recibida:", JSON.stringify(response, null, 2));

    return this.processIaVsPlayerResponse(response);
  }

  // Método adicional para debuggear la configuración del preset
  async debugPresetConfiguration(): Promise<void> {
    try {
      console.log("🔍 Obteniendo configuración del preset...");

      const response = await axios.get(
        `${BASE_URL}preset/${this.selectedPreset}`,
        {
          headers: this.getHeaders(),
        }
      );

      // console.log(
      //   "📋 Configuración completa del preset:",
      //   JSON.stringify(response.data, null, 2)
      // );

      // Específicamente buscar campos requeridos
      if (response.data.preset?.template) {
        console.log("📝 Template del preset:", response.data.preset.template);
      }

      if (response.data.preset?.preamble) {
        console.log("📋 Preamble del preset:", response.data.preset.preamble);
      }
    } catch (error) {
      console.error("❌ Error obteniendo configuración del preset:", error);
    }
  }

  getCurrentPreset(): string {
    return this.selectedPreset;
  }

  /**
   * Actualiza el progreso del juego basado en la respuesta parseada
   * @param parsedResponse - Respuesta JSON parseada del juego
   */
  private updateGameProgress(parsedResponse: any): void {
    try {
      const questionsRemaining = parsedResponse.cuenta_regresiva;
      const hint = parsedResponse.pista;
      const gameFinished = parsedResponse.juego_finalizado;
      const gameWon = parsedResponse.acertado;

      this.conversationStorage.updateGameProgress(
        questionsRemaining,
        hint,
        gameFinished,
        gameWon
      );
    } catch (error) {
      console.error("❌ Error actualizando progreso del juego:", error);
    }
  }
}
