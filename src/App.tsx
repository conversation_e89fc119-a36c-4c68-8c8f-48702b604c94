// Core or Framework Imports
import { useState, useEffect, useCallback } from "react";
// Third-Party Library Imports
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
import { MovistarOttService } from "./services/MovistarOttService";
import { GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";
import "./App.css";

/**
 * Main App component that manages the character guessing game
 *
 * Features:
 * - Automatic character generation and game initialization
 * - Voice chat integration for interactive gameplay
 * - Game state management and reset functionality
 * - Audio playback with fallback handling
 */
function App() {
  // Game state management
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [, setCharacterError] = useState<string>(""); // Unused but kept for potential error handling

  // Service instances (singletons)
  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();
  const movistarOttService = MovistarOttService.getInstance();

  /**
   * Initialize app by clearing previous conversations
   * This ensures a fresh start for each session
   */
  useEffect(() => {
    conversationStorage.clearAllConversations();
    console.log("🧹 Conversaciones limpiadas al inicializar la aplicación");
  }, [conversationStorage]);

  /**
   * Configure audio playback callback
   * Sets up automatic audio playback with fallback handling
   */
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Small delay to ensure proper audio context initialization
      setTimeout(() => {
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        playAudioWithFallback(audioUrl, audioFinishedCallback);
      }, 100);
    });
  }, [appService]);

  /**
   * Debug preset configuration on app initialization
   */
  useEffect(() => {
    appService.debugPresetConfiguration();
  }, []);

  /**
   * Test MovistarOttService functionality
   */
  useEffect(() => {
    console.log("🧪 Probando MovistarOttService...");
    const testMovistarService = async () => {
      console.log("🔍 Probando MovistarOttService...");
      try {
        console.log("🎬 Iniciando prueba de MovistarOttService...");

        // Check initialization status
        console.log("📊 Estado inicial:", {
          initialized: movistarOttService.isInitialized(),
          loading: movistarOttService.isLoading(),
          error: movistarOttService.getError()
        });

        // Wait a bit for initialization if needed
        if (!movistarOttService.isInitialized() && !movistarOttService.getError()) {
          console.log("⏳ Esperando inicialización...");
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // Check status after waiting
        console.log("📊 Estado después de espera:", {
          initialized: movistarOttService.isInitialized(),
          loading: movistarOttService.isLoading(),
          error: movistarOttService.getError()
        });

        if (movistarOttService.isInitialized()) {
          // List available tools
          console.log("🔧 Listando herramientas disponibles...");
          const tools = await movistarOttService.listTools();
          console.log("🔧 Herramientas disponibles:", tools);

          // Test movie search
          console.log("🔍 Probando búsqueda de películas...");
          const movieResults = await movistarOttService.searchMovies("Avatar");
          console.log("🎬 Resultados de búsqueda para 'Avatar':", movieResults);

        } else {
          console.warn("⚠️ MovistarOttService no se pudo inicializar:", movistarOttService.getError());
        }

      } catch (error) {
        console.error("❌ Error probando MovistarOttService:", error);
      }
    };

    // Run test after a short delay to ensure other services are initialized
    setTimeout(testMovistarService, 1000);
  }, []);

  /**
   * Extract response text from API response object
   * Handles multiple possible response field names
   */
  const extractResponseText = useCallback((response: any, fallback = "Respuesta no encontrada") => {
    return response.response ||
           response.output ||
           response.result ||
           response.text ||
           response.content ||
           fallback;
  }, []);

  /**
   * Main game initialization function
   * Combines character generation and game start in a single flow
   */
  const handleStartGameDirectly = useCallback(async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Step 1: Generate character
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText = extractResponseText(characterResponse);
      if (!characterText || characterText === "Respuesta no encontrada") {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);

      // Step 2: Start game immediately
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText = extractResponseText(gameResponse);
      setInitialMessage(responseText);

    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError("Error al generar personaje o iniciar juego. Inténtalo de nuevo.");
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  }, [appService, extractResponseText]);

  /**
   * Reset game to initial state
   * Clears all game data and conversation history
   */
  const handleResetGame = useCallback(() => {
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setCharacterError("");
    conversationStorage.clearAllConversations();
    console.log("🔄 Juego reiniciado y conversaciones limpiadas");
  }, [conversationStorage]);

  return (
    <>
      <div className="card">
        <div className="game-container">
          <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

          {/* Quick Start Section */}
          {!gameStarted && (
            <div className="quick-start-section">
              <h4 className="quick-start-title">🚀 Inicio Rápido</h4>
              <p className="quick-start-description">
                ¡Comienza a jugar inmediatamente! Se generará un personaje
                automáticamente y comenzará el juego.
              </p>
              <button
                onClick={handleStartGameDirectly}
                disabled={aiLoading}
                className="primary-button"
              >
                {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
              </button>
            </div>
          )}

          {/* Voice Chat Component - handles all conversation history */}
          <SimpleVoiceChat
            generatedCharacter={generatedCharacter}
            isGameStarted={gameStarted}
            initialMessage={initialMessage}
          />

          {/* Reset Game Section */}
          {(generatedCharacter || gameStarted) && (
            <div className="reset-section">
              <button onClick={handleResetGame} className="reset-button">
                🔄 Reiniciar Juego
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default App;
